from tkinter import *
import os
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()

    def createWidget(self):
        self.label01 = Label(self, text="用户名")
        self.label01.pack()

        self.label02 = Label(self, text="密码", bg="black", fg="white")
        self.label02.pack()
        
        self.label03 = Label(
            self, text="第一行\n第二行\n第三行", bg="blue", fg="white", width=10, height=6,
            justify=LEFT
        )
        self.label03.pack()
        self.label03.pack()

        global photo
        print(os.path.dirname(__file__))
        photo = PhotoImage(file=os.path.dirname(__file__)+ r"/img/1.gif")
        self.label04 = Label(self, image=photo)
        self.label04.pack()
        


if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()