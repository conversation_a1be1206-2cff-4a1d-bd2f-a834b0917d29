1、GUI编程
    <!-- root = Tk()
    root.title("第一个GUI程序")
    btn01 = Button(root, text="点我就送花", bg="red", fg="yellow")
    btn01.pack()
    def songhua(event):
        print("送你一朵花")
    btn01.bind("<Button-1>", songhua)
    root.mainloop() -->

    <font color="blue">流程：执行了root.mainloop()后，程序就会进入主循环，等待事件的触发，当用户点击按钮时，会触发事件，调用songhua函数，打印"送你一朵花"。</font>
    <font color="green">btn01.pack() 是让按钮显示在窗口中。</font>
    <font color="purple">event 事件对象，包含了事件的所有信息，如事件的类型，事件的坐标等。</font>
    <font color="orange">btn01.bind("<Button-1>", songhua) 是将songhua函数绑定到btn01按钮上，当btn01按钮被点击时，会调用songhua函数。</font>
    <font color="red"><Button-1> 是事件类型，表示鼠标左键点击事件。</font>


2、gui的标准类模式
    代码路径：python-code/ gui编程/003_gui的标准类模式.py
    
    Tkinter GUI 编程核心概念解析
    1. 关于 root = Tk() 的理解
        root = Tk()root.title("第一个GUI程序")
        root.geometry("500x500+500+300")
        解答：
            root = Tk() 确实创建了 GUI 程序的"最外层架子"（主窗口）
            root 是一个 Tk 实例，作为所有 GUI 组件的容器
            通过 root.title() 和 root.geometry() 可以设置窗口标题和大小
    2. 类继承中 super().__init__(master) 的作用
    class Application(Frame):    
        def __init__(self, master):
            super().__init__(master)
            self.master = master
        解答：
            Application 类继承自 Frame 类
            super().__init__(master) 调用父类 Frame 的初始化方法，将 master（即 root）设为父容器
            这样创建的 Frame 实例就知道它的父容器是 root
            self.master = master 保存对主窗口的引用，便于后续操作
    3. self.pack() 的工作机制
        self.pack()
        解答：
            self 是 Application 类的实例，继承自 Frame
            self.pack() 将这个 Frame 实例放置到其父容器（master，即 root）中
            无需额外参数是因为在 super().__init__(master) 中已经建立了父子关系
            Frame 可以理解为主窗口中的一个子区域或子窗口
    4. 整体关系图
        root (Tk实例) - 主窗口
        └── self (Application实例, 继承自Frame) - 子区域
            ├── btn01 (Button) - 按钮组件
            └── btnQuit (Button) - 退出按钮
