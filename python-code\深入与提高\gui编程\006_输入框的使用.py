from tkinter import *

class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()
    
    def createWidget(self):
        self.label01 = Label(self, text="用户名")
        self.label01.pack()

        self.entry01 = Entry(self)
        self.entry01.pack()
        self.btn01 = Button(self, text="登录", command=self.login)
        self.btn01.pack()

        self.label02 = Label(self, text="密码")
        self.label02.pack()
        self.entry02 =Entry(self, show="*")
        self.entry02.pack()
    def login(self):
        print(self.entry01.get())
        print(self.entry02.get())

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()